/* eslint-disable react/prop-types */
/* eslint-disable react/no-unknown-property */
import * as THREE from 'three'
import { useEffect, useRef, useState } from 'react'
import { Canvas, extend, useThree, use<PERSON>rame, useLoader } from '@react-three/fiber'
import { Text, useTexture, Environment } from '@react-three/drei'
import { BallCollider, CuboidCollider, Physics, RigidBody, useRopeJoint, useSphericalJoint } from '@react-three/rapier'
import { MeshLineGeometry, MeshLineMaterial } from 'meshline'
import { personalInfo } from '../../data/portfolio'

extend({ MeshLineGeometry, MeshLineMaterial })

// Photo component with texture
function ProfilePhoto() {
  // For now, let's use a simple colored rectangle as placeholder
  // You can replace this with useTexture('/profile-photo.jpg') when you add your photo

  return (
    <mesh position={[0, 0.15, 0.002]}>
      <planeGeometry args={[0.33, 0.43]} />
      <meshStandardMaterial color="#333333" />

      {/* Temporary text overlay */}
      <Text
        position={[0, 0, 0.001]}
        fontSize={0.025}
        color="#cccccc"
        anchorX="center"
        anchorY="middle"
        maxWidth={0.3}
      >
        Your Photo Here
      </Text>
    </mesh>
  )
}

// Simple working version first
function SimpleCard() {
  const meshRef = useRef()
  const [hovered, setHovered] = useState(false)

  useFrame((state) => {
    if (meshRef.current) {
      meshRef.current.rotation.y = Math.sin(state.clock.elapsedTime) * 0.1
      meshRef.current.position.y = Math.sin(state.clock.elapsedTime * 2) * 0.1
    }
  })

  return (
    <group>
      {/* Lanyard */}
      <mesh position={[0, 2, 0]}>
        <cylinderGeometry args={[0.02, 0.02, 4]} />
        <meshStandardMaterial color="#333333" />
      </mesh>

      {/* Card */}
      <mesh
        ref={meshRef}
        position={[0, -1, 0]}
        onPointerOver={() => setHovered(true)}
        onPointerOut={() => setHovered(false)}
        scale={hovered ? 1.1 : 1}
      >
        <boxGeometry args={[1.6, 2.4, 0.1]} />
        <meshStandardMaterial color="#e8e8e8" />

        {/* Card Content */}
        <group position={[0, 0, 0.06]}>
          {/* Header */}
          <mesh position={[0, 1.0, 0]}>
            <boxGeometry args={[1.58, 0.15, 0.01]} />
            <meshStandardMaterial color="#000000" />
          </mesh>

          {/* Company logo "G" */}
          <mesh position={[-0.6, 1.0, 0.01]}>
            <boxGeometry args={[0.12, 0.12, 0.01]} />
            <meshStandardMaterial color="#ffffff" />
          </mesh>

          <Text
            position={[-0.6, 1.0, 0.02]}
            fontSize={0.08}
            color="#000000"
            anchorX="center"
            anchorY="middle"
          >
            G
          </Text>

          {/* Company name */}
          <Text
            position={[0.1, 1.0, 0.01]}
            fontSize={0.05}
            color="#ffffff"
            anchorX="center"
            anchorY="middle"
          >
            gateramark
          </Text>

          {/* Photo area */}
          <mesh position={[0, 0.3, 0]}>
            <boxGeometry args={[0.7, 0.9, 0.01]} />
            <meshStandardMaterial color="#f5f5f5" />
          </mesh>

          {/* Photo border */}
          <mesh position={[0, 0.3, 0.01]}>
            <boxGeometry args={[0.68, 0.88, 0.01]} />
            <meshStandardMaterial color="#ffffff" />
          </mesh>

          {/* Geometric shapes */}
          <Text
            position={[-0.12, 0.4, 0.02]}
            fontSize={0.08}
            color="#cccccc"
            anchorX="center"
            anchorY="middle"
          >
            ⬜
          </Text>
          <Text
            position={[0, 0.4, 0.02]}
            fontSize={0.08}
            color="#cccccc"
            anchorX="center"
            anchorY="middle"
          >
            ⬜
          </Text>
          <Text
            position={[0.12, 0.4, 0.02]}
            fontSize={0.08}
            color="#cccccc"
            anchorX="center"
            anchorY="middle"
          >
            ⬜
          </Text>

          {/* Name */}
          <Text
            position={[0, -0.3, 0]}
            fontSize={0.07}
            color="#000000"
            anchorX="center"
            anchorY="middle"
            maxWidth={1.4}
          >
            {personalInfo.name}
          </Text>

          {/* Additional info */}
          <Text
            position={[0, -0.5, 0]}
            fontSize={0.04}
            color="#666666"
            anchorX="center"
            anchorY="middle"
          >
            {personalInfo.yearsOfExperience}+ Years • {personalInfo.location}
          </Text>
        </group>
      </mesh>

      {/* Clip */}
      <mesh position={[0, 0.2, 0.06]}>
        <boxGeometry args={[0.2, 0.1, 0.05]} />
        <meshStandardMaterial color="#666666" metalness={0.8} roughness={0.2} />
      </mesh>
    </group>
  )
}

// Badge Component - Based on reference implementation
function Badge({ maxSpeed = 50, minSpeed = 10 }) {
  const band = useRef()
  const fixed = useRef()
  const j1 = useRef()
  const j2 = useRef()
  const j3 = useRef()
  const card = useRef()

  // Vector helpers
  const vec = new THREE.Vector3()
  const ang = new THREE.Vector3()
  const rot = new THREE.Vector3()
  const dir = new THREE.Vector3()

  // Physics properties
  const segmentProps = {
    type: 'dynamic',
    canSleep: true,
    colliders: false,
    angularDamping: 2,
    linearDamping: 2
  }

  // Screen size for MeshLine
  const { width, height } = useThree((state) => state.size)

  // Curve for rope
  const [curve] = useState(() => new THREE.CatmullRomCurve3([
    new THREE.Vector3(),
    new THREE.Vector3(),
    new THREE.Vector3(),
    new THREE.Vector3()
  ]))

  // Interaction state
  const [dragged, drag] = useState(false)
  const [hovered, hover] = useState(false)

  // Physics connections - exact same as reference
  useRopeJoint(fixed, j1, [[0, 0, 0], [0, 0, 0], 1])
  useRopeJoint(j1, j2, [[0, 0, 0], [0, 0, 0], 1])
  useRopeJoint(j2, j3, [[0, 0, 0], [0, 0, 0], 1])
  useSphericalJoint(j3, card, [[0, 0, 0], [0, 1.45, 0]])

  useEffect(() => {
    if (hovered) {
      document.body.style.cursor = dragged ? 'grabbing' : 'grab'
      return () => void (document.body.style.cursor = 'auto')
    }
  }, [hovered, dragged])

  useFrame((state, delta) => {
    if (dragged) {
      vec.set(state.pointer.x, state.pointer.y, 0.5).unproject(state.camera)
      dir.copy(vec).sub(state.camera.position).normalize()
      vec.add(dir.multiplyScalar(state.camera.position.length()))
      ;[card, j1, j2, j3, fixed].forEach((ref) => ref.current?.wakeUp())
      card.current?.setNextKinematicTranslation({
        x: vec.x - dragged.x,
        y: vec.y - dragged.y,
        z: vec.z - dragged.z
      })
    }

    if (fixed.current) {
      // Fix most of the jitter when over pulling the card - exact same as reference
      ;[j1, j2].forEach((ref) => {
        if (!ref.current.lerped) ref.current.lerped = new THREE.Vector3().copy(ref.current.translation())
        const clampedDistance = Math.max(0.1, Math.min(1, ref.current.lerped.distanceTo(ref.current.translation())))
        ref.current.lerped.lerp(ref.current.translation(), delta * (minSpeed + clampedDistance * (maxSpeed - minSpeed)))
      })

      // Calculate catmul curve - exact same order as reference
      curve.points[0].copy(j3.current.translation())
      curve.points[1].copy(j2.current.lerped)
      curve.points[2].copy(j1.current.lerped)
      curve.points[3].copy(fixed.current.translation())
      band.current.geometry.setPoints(curve.getPoints(32))

      // Tilt it back towards the screen
      ang.copy(card.current.angvel())
      rot.copy(card.current.rotation())
      card.current.setAngvel({ x: ang.x, y: ang.y - rot.y * 0.25, z: ang.z })
    }
  })

  curve.curveType = 'chordal'

  return (
    <>
      {/* MeshLine Rope - exact same as reference */}
      <mesh ref={band}>
        <meshLineGeometry />
        <meshLineMaterial
          color="#ffffff"
          depthTest={false}
          resolution={[width, height]}
          lineWidth={2}
          opacity={1.0}
          transparent={false}
        />
      </mesh>

      {/* Physics Bodies */}
      <group position={[0, 4, 0]}>
        {/* Fixed anchor point */}
        <RigidBody ref={fixed} type="fixed" position={[0, 0, 0]} />

        {/* Rope joints */}
        <RigidBody ref={j1} {...segmentProps} position={[0.5, 0, 0]}>
          <BallCollider args={[0.1]} />
        </RigidBody>

        <RigidBody ref={j2} {...segmentProps} position={[1, 0, 0]}>
          <BallCollider args={[0.1]} />
        </RigidBody>

        <RigidBody ref={j3} {...segmentProps} position={[1.5, 0, 0]}>
          <BallCollider args={[0.1]} />
        </RigidBody>

        {/* ID Card */}
        <RigidBody
          ref={card}
          {...segmentProps}
          type={dragged ? 'kinematicPosition' : 'dynamic'}
          position={[2, 0, 0]}
        >
          <CuboidCollider args={[0.8, 1.125, 0.01]} />
          <group
            scale={2.25}
            position={[0, -1.2, -0.05]}
            onPointerOver={() => hover(true)}
            onPointerOut={() => hover(false)}
            onPointerUp={(e) => (e.target.releasePointerCapture(e.pointerId), drag(false))}
            onPointerDown={(e) => (e.target.setPointerCapture(e.pointerId), drag(new THREE.Vector3().copy(e.point).sub(vec.copy(card.current.translation()))))}
          >
            {/* Main ID Card */}
            <mesh>
              <boxGeometry args={[0.8, 1.2, 0.05]} />
              <meshPhysicalMaterial
                color="#1a1a1a"
                roughness={0.05}
                metalness={0.2}
                clearcoat={1.0}
                clearcoatRoughness={0.05}
                reflectivity={1.0}
                envMapIntensity={1.5}
              />
            </mesh>

            {/* Card Content */}
            <group position={[0, 0, 0.026]}>
              {/* Header bar */}
              <mesh position={[0, 0.5, 0]}>
                <boxGeometry args={[0.78, 0.08, 0.001]} />
                <meshStandardMaterial color="#1a1a1a" />
              </mesh>

              {/* Company logo "G" */}
              <mesh position={[-0.28, 0.5, 0.001]}>
                <boxGeometry args={[0.06, 0.06, 0.001]} />
                <meshStandardMaterial color="#ffffff" />
              </mesh>

              <Text
                position={[-0.28, 0.5, 0.002]}
                fontSize={0.04}
                color="#000000"
                anchorX="center"
                anchorY="middle"
              >
                G
              </Text>

              {/* Company name */}
              <Text
                position={[0.1, 0.5, 0.001]}
                fontSize={0.025}
                color="#ffffff"
                anchorX="center"
                anchorY="middle"
              >
                gateramark
              </Text>

              {/* Photo area */}
              <mesh position={[0, 0.15, 0]}>
                <boxGeometry args={[0.35, 0.45, 0.001]} />
                <meshStandardMaterial color="#0a0a0a" />
              </mesh>

              {/* Photo border */}
              <mesh position={[0, 0.15, 0.001]}>
                <boxGeometry args={[0.33, 0.43, 0.001]} />
                <meshStandardMaterial color="#1a1a1a" />
              </mesh>

              {/* Profile Photo */}
              <ProfilePhoto />

              {/* Geometric shapes placeholder */}
              <Text
                position={[-0.06, 0.2, 0.002]}
                fontSize={0.04}
                color="#cccccc"
                anchorX="center"
                anchorY="middle"
              >
                ⬜
              </Text>
              <Text
                position={[0, 0.2, 0.002]}
                fontSize={0.04}
                color="#cccccc"
                anchorX="center"
                anchorY="middle"
              >
                ⬜
              </Text>
              <Text
                position={[0.06, 0.2, 0.002]}
                fontSize={0.04}
                color="#cccccc"
                anchorX="center"
                anchorY="middle"
              >
                ⬜
              </Text>

              {/* Name */}
              <Text
                position={[0, -0.15, 0]}
                fontSize={0.035}
                color="#ffffff"
                anchorX="center"
                anchorY="middle"
                maxWidth={0.7}
              >
                {personalInfo.name}
              </Text>

              {/* Additional info */}
              <Text
                position={[0, -0.25, 0]}
                fontSize={0.02}
                color="#cccccc"
                anchorX="center"
                anchorY="middle"
                maxWidth={0.7}
              >
                {personalInfo.yearsOfExperience}+ Years • {personalInfo.location}
              </Text>
            </group>

            {/* Hook/Clip system */}
            <group position={[0, 0.6, 0]}>
              {/* Main clip body */}
              <mesh position={[0, 0.05, 0.03]}>
                <boxGeometry args={[0.12, 0.08, 0.04]} />
                <meshPhysicalMaterial
                  color="#333333"
                  metalness={0.9}
                  roughness={0.1}
                  clearcoat={1.0}
                  clearcoatRoughness={0.1}
                />
              </mesh>

              {/* Clip hole */}
              <mesh position={[0, 0.05, 0.05]}>
                <cylinderGeometry args={[0.015, 0.015, 0.06]} />
                <meshStandardMaterial color="#000000" />
              </mesh>

              {/* Hook ring */}
              <mesh position={[0, 0.08, 0.03]} rotation={[Math.PI / 2, 0, 0]}>
                <torusGeometry args={[0.025, 0.005, 8, 16]} />
                <meshPhysicalMaterial
                  color="#666666"
                  metalness={1.0}
                  roughness={0.1}
                  clearcoat={1.0}
                  clearcoatRoughness={0.1}
                />
              </mesh>

              {/* Hook connector */}
              <mesh position={[0, 0.1, 0.03]}>
                <cylinderGeometry args={[0.003, 0.003, 0.04]} />
                <meshPhysicalMaterial
                  color="#666666"
                  metalness={1.0}
                  roughness={0.1}
                />
              </mesh>

              {/* Rope connection point */}
              <mesh position={[0, 0.18, 0.054]}>
                <sphereGeometry args={[0.012, 8, 8]} />
                <meshStandardMaterial
                  color="#ffffff"
                  emissive="#333333"
                  emissiveIntensity={0.3}
                />
              </mesh>
            </group>
          </group>
        </RigidBody>
      </group>
    </>
  )
}

export default function WorkingCardApp() {
  return (
    <div style={{ width: '100%', height: '100%' }}>
      <Canvas camera={{ position: [0, 0, 13], fov: 25 }}>
        <color attach="background" args={['#1a1a1a']} />
        <Environment preset="studio" />
        <ambientLight intensity={0.4} />
        <directionalLight position={[10, 10, 5]} intensity={1.0} />
        <directionalLight position={[-10, 5, 5]} intensity={0.6} />
        <directionalLight position={[0, -10, 5]} intensity={0.4} />
        <directionalLight position={[0, 10, 0]} intensity={0.8} />
        <pointLight position={[5, 5, 10]} intensity={0.8} />
        <pointLight position={[-5, -5, 8]} intensity={0.6} />
        <pointLight position={[0, 5, 5]} intensity={0.5} color="#ffffff" />
        <spotLight position={[0, 15, 10]} intensity={0.6} angle={0.3} penumbra={1} />
        <Physics
          gravity={[0, -9.81, 0]}
          timeStep={1 / 60}
          numSolverIterations={8}
          numAdditionalFrictionIterations={4}
          erp={0.8}
        >
          <Badge />
        </Physics>
      </Canvas>
    </div>
  )
}


