/* eslint-disable react/prop-types */
/* eslint-disable react/no-unknown-property */
import * as THREE from 'three'
import { useEffect, useRef, useState } from 'react'
import { <PERSON>vas, useThree, use<PERSON>rame, useLoader } from '@react-three/fiber'
import { Text, useTexture, Environment } from '@react-three/drei'
import { BallCollider, CuboidCollider, Physics, RigidBody, useRopeJoint, useSphericalJoint } from '@react-three/rapier'
import { personalInfo } from '../../data/portfolio'

// Photo component with texture
function ProfilePhoto() {
  // For now, let's use a simple colored rectangle as placeholder
  // You can replace this with useTexture('/profile-photo.jpg') when you add your photo

  return (
    <mesh position={[0, 0.15, 0.002]}>
      <planeGeometry args={[0.33, 0.43]} />
      <meshStandardMaterial color="#333333" />

      {/* Temporary text overlay */}
      <Text
        position={[0, 0, 0.001]}
        fontSize={0.025}
        color="#cccccc"
        anchorX="center"
        anchorY="middle"
        maxWidth={0.3}
      >
        Your Photo Here
      </Text>
    </mesh>
  )
}

// Simple working version first
function SimpleCard() {
  const meshRef = useRef()
  const [hovered, setHovered] = useState(false)

  useFrame((state) => {
    if (meshRef.current) {
      meshRef.current.rotation.y = Math.sin(state.clock.elapsedTime) * 0.1
      meshRef.current.position.y = Math.sin(state.clock.elapsedTime * 2) * 0.1
    }
  })

  return (
    <group>
      {/* Lanyard */}
      <mesh position={[0, 2, 0]}>
        <cylinderGeometry args={[0.02, 0.02, 4]} />
        <meshStandardMaterial color="#333333" />
      </mesh>

      {/* Card */}
      <mesh
        ref={meshRef}
        position={[0, -1, 0]}
        onPointerOver={() => setHovered(true)}
        onPointerOut={() => setHovered(false)}
        scale={hovered ? 1.1 : 1}
      >
        <boxGeometry args={[1.6, 2.4, 0.1]} />
        <meshStandardMaterial color="#e8e8e8" />

        {/* Card Content */}
        <group position={[0, 0, 0.06]}>
          {/* Header */}
          <mesh position={[0, 1.0, 0]}>
            <boxGeometry args={[1.58, 0.15, 0.01]} />
            <meshStandardMaterial color="#000000" />
          </mesh>

          {/* Company logo "G" */}
          <mesh position={[-0.6, 1.0, 0.01]}>
            <boxGeometry args={[0.12, 0.12, 0.01]} />
            <meshStandardMaterial color="#ffffff" />
          </mesh>

          <Text
            position={[-0.6, 1.0, 0.02]}
            fontSize={0.08}
            color="#000000"
            anchorX="center"
            anchorY="middle"
          >
            G
          </Text>

          {/* Company name */}
          <Text
            position={[0.1, 1.0, 0.01]}
            fontSize={0.05}
            color="#ffffff"
            anchorX="center"
            anchorY="middle"
          >
            gateramark
          </Text>

          {/* Photo area */}
          <mesh position={[0, 0.3, 0]}>
            <boxGeometry args={[0.7, 0.9, 0.01]} />
            <meshStandardMaterial color="#f5f5f5" />
          </mesh>

          {/* Photo border */}
          <mesh position={[0, 0.3, 0.01]}>
            <boxGeometry args={[0.68, 0.88, 0.01]} />
            <meshStandardMaterial color="#ffffff" />
          </mesh>

          {/* Geometric shapes */}
          <Text
            position={[-0.12, 0.4, 0.02]}
            fontSize={0.08}
            color="#cccccc"
            anchorX="center"
            anchorY="middle"
          >
            ⬜
          </Text>
          <Text
            position={[0, 0.4, 0.02]}
            fontSize={0.08}
            color="#cccccc"
            anchorX="center"
            anchorY="middle"
          >
            ⬜
          </Text>
          <Text
            position={[0.12, 0.4, 0.02]}
            fontSize={0.08}
            color="#cccccc"
            anchorX="center"
            anchorY="middle"
          >
            ⬜
          </Text>

          {/* Name */}
          <Text
            position={[0, -0.3, 0]}
            fontSize={0.07}
            color="#000000"
            anchorX="center"
            anchorY="middle"
            maxWidth={1.4}
          >
            {personalInfo.name}
          </Text>

          {/* Additional info */}
          <Text
            position={[0, -0.5, 0]}
            fontSize={0.04}
            color="#666666"
            anchorX="center"
            anchorY="middle"
          >
            {personalInfo.yearsOfExperience}+ Years • {personalInfo.location}
          </Text>
        </group>
      </mesh>

      {/* Clip */}
      <mesh position={[0, 0.2, 0.06]}>
        <boxGeometry args={[0.2, 0.1, 0.05]} />
        <meshStandardMaterial color="#666666" metalness={0.8} roughness={0.2} />
      </mesh>
    </group>
  )
}

// Thread Component - Simple cylindrical thread implementation
function Thread({ start, end }) {
  const threadRef = useRef()

  useFrame(() => {
    if (!threadRef.current || !start || !end) return

    // Calculate thread geometry
    const startPos = new THREE.Vector3(start.x, start.y, start.z)
    const endPos = new THREE.Vector3(end.x, end.y, end.z)
    const direction = new THREE.Vector3().subVectors(endPos, startPos)
    const length = direction.length()
    const center = new THREE.Vector3().addVectors(startPos, endPos).multiplyScalar(0.5)

    // Position and orient the thread
    threadRef.current.position.copy(center)
    threadRef.current.lookAt(endPos)
    threadRef.current.rotateX(Math.PI / 2)

    // Scale to match length
    threadRef.current.scale.set(1, length, 1)
  })

  return (
    <mesh ref={threadRef}>
      <cylinderGeometry args={[0.008, 0.008, 1, 8]} />
      <meshStandardMaterial
        color="#2a2a2a"
        roughness={0.8}
        metalness={0.1}
      />
    </mesh>
  )
}

// Physics-based Badge Component
function Badge() {
  const fixed = useRef()
  const j1 = useRef()
  const j2 = useRef()
  const j3 = useRef()
  const card = useRef()

  const vec = new THREE.Vector3()
  const ang = new THREE.Vector3()
  const rot = new THREE.Vector3()
  const dir = new THREE.Vector3()

  const [dragged, drag] = useState(false)
  const [hovered, hover] = useState(false)
  const [threadPositions, setThreadPositions] = useState({
    segment1: { start: { x: 0, y: 4, z: 0 }, end: { x: 0.5, y: 4, z: 0 } },
    segment2: { start: { x: 0.5, y: 4, z: 0 }, end: { x: 1, y: 4, z: 0 } },
    segment3: { start: { x: 1, y: 4, z: 0 }, end: { x: 1.5, y: 4, z: 0 } },
    segment4: { start: { x: 1.5, y: 4, z: 0 }, end: { x: 2, y: 4, z: 0 } }
  })

  // Connect joints with rope physics
  useRopeJoint(fixed, j1, [[0, 0, 0], [0, 0, 0], 0.8])
  useRopeJoint(j1, j2, [[0, 0, 0], [0, 0, 0], 0.8])
  useRopeJoint(j2, j3, [[0, 0, 0], [0, 0, 0], 0.8])
  useSphericalJoint(j3, card, [[0, 0, 0], [0, 1.45, 0]])

  useEffect(() => {
    if (hovered) {
      document.body.style.cursor = dragged ? 'grabbing' : 'grab'
      return () => void (document.body.style.cursor = 'auto')
    }
  }, [hovered, dragged])

  useFrame((state) => {
    if (!fixed.current || !j1.current || !j2.current || !j3.current || !card.current) return

    if (dragged) {
      vec.set(state.pointer.x, state.pointer.y, 0.5).unproject(state.camera)
      dir.copy(vec).sub(state.camera.position).normalize()
      vec.add(dir.multiplyScalar(state.camera.position.length()))
      ;[card, j1, j2, j3, fixed].forEach((ref) => ref.current?.wakeUp())
      card.current.setNextKinematicTranslation({
        x: vec.x - dragged.x,
        y: vec.y - dragged.y,
        z: vec.z - dragged.z,
      })
    }

    // Update thread positions based on physics bodies
    const fixedPos = fixed.current.translation()
    const j1Pos = j1.current.translation()
    const j2Pos = j2.current.translation()
    const j3Pos = j3.current.translation()
    const cardPos = card.current.translation()

    setThreadPositions({
      segment1: {
        start: { x: fixedPos.x, y: fixedPos.y, z: fixedPos.z },
        end: { x: j1Pos.x, y: j1Pos.y, z: j1Pos.z }
      },
      segment2: {
        start: { x: j1Pos.x, y: j1Pos.y, z: j1Pos.z },
        end: { x: j2Pos.x, y: j2Pos.y, z: j2Pos.z }
      },
      segment3: {
        start: { x: j2Pos.x, y: j2Pos.y, z: j2Pos.z },
        end: { x: j3Pos.x, y: j3Pos.y, z: j3Pos.z }
      },
      segment4: {
        start: { x: j3Pos.x, y: j3Pos.y, z: j3Pos.z },
        end: { x: cardPos.x, y: cardPos.y + 1.45, z: cardPos.z }
      }
    })

    // Tilt card
    ang.copy(card.current.angvel())
    rot.copy(card.current.rotation())
    card.current.setAngvel({ x: ang.x, y: ang.y - rot.y * 0.25, z: ang.z }, false)
  })

  const segmentProps = { type: 'dynamic', canSleep: true, colliders: false, angularDamping: 2, linearDamping: 2 }

  return (
    <>
      {/* Thread segments */}
      <Thread start={threadPositions.segment1.start} end={threadPositions.segment1.end} />
      <Thread start={threadPositions.segment2.start} end={threadPositions.segment2.end} />
      <Thread start={threadPositions.segment3.start} end={threadPositions.segment3.end} />
      <Thread start={threadPositions.segment4.start} end={threadPositions.segment4.end} />

      {/* Physics bodies */}
      <group position={[0, 4, 0]}>
        <RigidBody ref={fixed} {...segmentProps} type="fixed" />
        <RigidBody position={[0.5, 0, 0]} ref={j1} {...segmentProps}>
          <BallCollider args={[0.1]} />
        </RigidBody>
        <RigidBody position={[1, 0, 0]} ref={j2} {...segmentProps}>
          <BallCollider args={[0.1]} />
        </RigidBody>
        <RigidBody position={[1.5, 0, 0]} ref={j3} {...segmentProps}>
          <BallCollider args={[0.1]} />
        </RigidBody>
        <RigidBody position={[2, 0, 0]} ref={card} {...segmentProps} type={dragged ? 'kinematicPosition' : 'dynamic'}>
          <CuboidCollider args={[0.8, 1.125, 0.01]} />
          <group
            scale={2.25}
            position={[0, -1.2, -0.05]}
            onPointerOver={(e) => {
              e.stopPropagation()
              hover(true)
            }}
            onPointerOut={(e) => {
              e.stopPropagation()
              hover(false)
            }}
            onPointerUp={(e) => {
              e.stopPropagation()
              if (e.target.releasePointerCapture) e.target.releasePointerCapture(e.pointerId)
              drag(false)
            }}
            onPointerDown={(e) => {
              e.stopPropagation()
              if (e.target.setPointerCapture) e.target.setPointerCapture(e.pointerId)
              if (card.current) {
                drag(new THREE.Vector3().copy(e.point).sub(vec.copy(card.current.translation())))
              }
            }}
          >
            {/* Main ID Card */}
            <mesh>
              <boxGeometry args={[0.8, 1.2, 0.05]} />
              <meshPhysicalMaterial
                color="#1a1a1a"
                roughness={0.05}
                metalness={0.2}
                clearcoat={1.0}
                clearcoatRoughness={0.05}
                reflectivity={1.0}
                envMapIntensity={1.5}
              />
            </mesh>

            {/* Card Content */}
            <group position={[0, 0, 0.026]}>
              {/* Header bar */}
              <mesh position={[0, 0.5, 0]}>
                <boxGeometry args={[0.78, 0.08, 0.001]} />
                <meshStandardMaterial color="#1a1a1a" />
              </mesh>

              {/* Company logo "G" */}
              <mesh position={[-0.28, 0.5, 0.001]}>
                <boxGeometry args={[0.06, 0.06, 0.001]} />
                <meshStandardMaterial color="#ffffff" />
              </mesh>

              <Text
                position={[-0.28, 0.5, 0.002]}
                fontSize={0.04}
                color="#000000"
                anchorX="center"
                anchorY="middle"
              >
                G
              </Text>

              {/* Company name */}
              <Text
                position={[0.1, 0.5, 0.001]}
                fontSize={0.025}
                color="#ffffff"
                anchorX="center"
                anchorY="middle"
              >
                gateramark
              </Text>

              {/* Photo area */}
              <mesh position={[0, 0.15, 0]}>
                <boxGeometry args={[0.35, 0.45, 0.001]} />
                <meshStandardMaterial color="#0a0a0a" />
              </mesh>

              {/* Photo border */}
              <mesh position={[0, 0.15, 0.001]}>
                <boxGeometry args={[0.33, 0.43, 0.001]} />
                <meshStandardMaterial color="#1a1a1a" />
              </mesh>

              {/* Profile Photo */}
              <ProfilePhoto />

              {/* Geometric shapes placeholder */}
              <Text
                position={[-0.06, 0.2, 0.002]}
                fontSize={0.04}
                color="#cccccc"
                anchorX="center"
                anchorY="middle"
              >
                ⬜
              </Text>
              <Text
                position={[0, 0.2, 0.002]}
                fontSize={0.04}
                color="#cccccc"
                anchorX="center"
                anchorY="middle"
              >
                ⬜
              </Text>
              <Text
                position={[0.06, 0.2, 0.002]}
                fontSize={0.04}
                color="#cccccc"
                anchorX="center"
                anchorY="middle"
              >
                ⬜
              </Text>

              {/* Name */}
              <Text
                position={[0, -0.15, 0]}
                fontSize={0.035}
                color="#ffffff"
                anchorX="center"
                anchorY="middle"
                maxWidth={0.7}
              >
                {personalInfo.name}
              </Text>

              {/* Additional info */}
              <Text
                position={[0, -0.25, 0]}
                fontSize={0.02}
                color="#cccccc"
                anchorX="center"
                anchorY="middle"
                maxWidth={0.7}
              >
                {personalInfo.yearsOfExperience}+ Years • {personalInfo.location}
              </Text>
            </group>

            {/* Hook/Clip system */}
            <group position={[0, 0.6, 0]}>
              {/* Main clip body */}
              <mesh position={[0, 0.05, 0.03]}>
                <boxGeometry args={[0.12, 0.08, 0.04]} />
                <meshPhysicalMaterial
                  color="#333333"
                  metalness={0.9}
                  roughness={0.1}
                  clearcoat={1.0}
                  clearcoatRoughness={0.1}
                />
              </mesh>

              {/* Clip hole */}
              <mesh position={[0, 0.05, 0.05]}>
                <cylinderGeometry args={[0.015, 0.015, 0.06]} />
                <meshStandardMaterial color="#000000" />
              </mesh>

              {/* Hook ring */}
              <mesh position={[0, 0.08, 0.03]} rotation={[Math.PI / 2, 0, 0]}>
                <torusGeometry args={[0.025, 0.005, 8, 16]} />
                <meshPhysicalMaterial
                  color="#666666"
                  metalness={1.0}
                  roughness={0.1}
                  clearcoat={1.0}
                  clearcoatRoughness={0.1}
                />
              </mesh>

              {/* Hook connector */}
              <mesh position={[0, 0.1, 0.03]}>
                <cylinderGeometry args={[0.003, 0.003, 0.04]} />
                <meshPhysicalMaterial
                  color="#666666"
                  metalness={1.0}
                  roughness={0.1}
                />
              </mesh>
            </group>
          </group>
        </RigidBody>
      </group>
    </>
  )
}

export default function WorkingCardApp() {
  return (
    <div style={{ width: '100%', height: '100%' }}>
      <Canvas camera={{ position: [0, 0, 13], fov: 25 }}>
        <color attach="background" args={['#1a1a1a']} />
        <Environment preset="studio" />
        <ambientLight intensity={0.3} />
        <directionalLight position={[10, 10, 5]} intensity={0.8} />
        <directionalLight position={[-10, 5, 5]} intensity={0.5} />
        <directionalLight position={[0, -10, 5]} intensity={0.3} />
        <pointLight position={[5, 5, 10]} intensity={0.6} />
        <pointLight position={[-5, -5, 8]} intensity={0.4} />
        <spotLight position={[0, 15, 10]} intensity={0.4} angle={0.3} penumbra={1} />
        <Physics gravity={[0, -40, 0]} timeStep={1 / 60}>
          <Badge />
        </Physics>
      </Canvas>
    </div>
  )
}


